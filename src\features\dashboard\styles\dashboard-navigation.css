/* Dashboard Navigation Styles */

/* Navigation */
.dashboard-nav {
  background-color: transparent;
  position: sticky;
  top: 0;
  z-index: 10;
  padding-top: 20px;
}

.nav-container {
  max-width: 1600px;
  margin: 20px auto 8px auto;
  padding: 0.8rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: transparent;
  box-shadow: none;
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s ease-in-out;
}

/* Scrolled state */
.nav-container.scrolled {
  margin: 18px auto 8px auto;
  max-width: 1200px;
  background-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 1rem;
}


.nav-logo {
  font-size: 1.8rem;
  font-weight: 300;
  text-shadow: none;
}

.nav-logo-aurea {
  color: #0079FF; /* blue color */
}

.nav-logo-voice {
  color: #fbbf24; /* golden color */
}

.nav-button {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #1e293b;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
  font-weight: 600;
  transition: background-color 0.2s;
}

.nav-button:hover {
  background-color: #1f2937; /* slate-800 */
}

/* Responsive navigation */
@media (max-width: 768px) {
  .nav-container {
    max-width: 95%;
    padding: 0.75rem 1.5rem;
    margin: 0 auto 15px auto;
    justify-content: space-between;
  }

  .nav-container.scrolled {
    max-width: 90%;
  }

  .nav-logo {
    font-size: 1.5rem;
  }

  .nav-logo-aurea {
    color: #0079FF;
  }

  .nav-logo-voice {
    color: #fbbf24;
  }

  .nav-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    max-width: 98%;
    padding: 0.75rem 1rem;
    margin: 0 auto 10px auto;
  }

  .nav-container.scrolled {
    max-width: 95%;
  }

  .nav-logo {
    font-size: 1.25rem;
  }

  .nav-logo-aurea {
    color: #0079FF;
  }

  .nav-logo-voice {
    color: #fbbf24;
  }

  .nav-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
}
